// ==UserScript==
// @name         专用数据解析器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  专门解析特定格式的API返回数据
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_notification
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置选项
    const CONFIG = {
        showNotifications: true,
        saveToStorage: true,
        autoDownload: false,
        logLevel: 'info' // 'debug', 'info', 'warn', 'error'
    };
    
    // 日志函数
    function log(level, message, data = null) {
        const levels = ['debug', 'info', 'warn', 'error'];
        if (levels.indexOf(level) >= levels.indexOf(CONFIG.logLevel)) {
            console[level](`[数据解析器] ${message}`, data || '');
        }
    }
    
    // 高级数据解析函数
    function advancedDataParser(dataString) {
        const result = {
            timestamp: new Date().toISOString(),
            original: dataString,
            length: dataString.length,
            analysis: {}
        };
        
        try {
            // 1. 基本信息分析
            result.analysis.isHex = /^[0-9a-fA-F]+$/.test(dataString);
            result.analysis.isBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(dataString);
            
            // 2. 如果是十六进制数据
            if (result.analysis.isHex) {
                log('info', '检测到十六进制数据');
                
                // 转换为字节数组
                const bytes = [];
                for (let i = 0; i < dataString.length; i += 2) {
                    bytes.push(parseInt(dataString.substr(i, 2), 16));
                }
                result.analysis.bytes = bytes;
                result.analysis.byteCount = bytes.length;
                
                // 尝试不同的解码方式
                result.decodingAttempts = {};
                
                // ASCII解码
                let asciiDecoded = '';
                for (let byte of bytes) {
                    if (byte >= 32 && byte <= 126) {
                        asciiDecoded += String.fromCharCode(byte);
                    } else {
                        asciiDecoded += `\\x${byte.toString(16).padStart(2, '0')}`;
                    }
                }
                result.decodingAttempts.ascii = asciiDecoded;
                
                // UTF-8解码尝试
                try {
                    const utf8Decoded = new TextDecoder('utf-8').decode(new Uint8Array(bytes));
                    result.decodingAttempts.utf8 = utf8Decoded;
                } catch (e) {
                    result.decodingAttempts.utf8 = '解码失败: ' + e.message;
                }
                
                // Base64解码尝试（将十六进制转为Base64再解码）
                try {
                    const base64String = btoa(String.fromCharCode.apply(null, bytes));
                    result.decodingAttempts.asBase64 = base64String;
                    result.decodingAttempts.base64Decoded = atob(base64String);
                } catch (e) {
                    result.decodingAttempts.base64Decoded = '解码失败: ' + e.message;
                }
                
                // 分析数据模式
                result.analysis.patterns = analyzePatterns(bytes);
            }
            
            // 3. 如果可能是Base64
            if (result.analysis.isBase64 && dataString.length % 4 === 0) {
                try {
                    const base64Decoded = atob(dataString);
                    result.decodingAttempts = result.decodingAttempts || {};
                    result.decodingAttempts.base64 = base64Decoded;
                } catch (e) {
                    log('warn', 'Base64解码失败', e.message);
                }
            }
            
            // 4. 尝试作为加密数据分析
            result.analysis.encryption = analyzeEncryption(dataString);
            
        } catch (error) {
            log('error', '数据解析过程中出错', error);
            result.error = error.message;
        }
        
        return result;
    }
    
    // 分析数据模式
    function analyzePatterns(bytes) {
        const patterns = {
            entropy: calculateEntropy(bytes),
            repeatingBytes: findRepeatingBytes(bytes),
            commonPrefixes: findCommonPrefixes(bytes),
            statisticalAnalysis: getByteStatistics(bytes)
        };
        
        return patterns;
    }
    
    // 计算熵值（用于判断是否为加密数据）
    function calculateEntropy(bytes) {
        const frequency = {};
        for (let byte of bytes) {
            frequency[byte] = (frequency[byte] || 0) + 1;
        }
        
        let entropy = 0;
        const length = bytes.length;
        for (let count of Object.values(frequency)) {
            const probability = count / length;
            entropy -= probability * Math.log2(probability);
        }
        
        return {
            value: entropy,
            maxPossible: 8,
            isHighEntropy: entropy > 7, // 高熵值可能表示加密数据
            interpretation: entropy > 7 ? '可能是加密数据' : entropy > 4 ? '中等随机性' : '低随机性'
        };
    }
    
    // 查找重复字节
    function findRepeatingBytes(bytes) {
        const patterns = {};
        for (let i = 0; i < bytes.length - 1; i++) {
            const pattern = bytes.slice(i, i + 2).join(',');
            patterns[pattern] = (patterns[pattern] || 0) + 1;
        }
        
        return Object.entries(patterns)
            .filter(([pattern, count]) => count > 1)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);
    }
    
    // 查找常见前缀
    function findCommonPrefixes(bytes) {
        const prefixes = {
            'PDF': [0x25, 0x50, 0x44, 0x46],
            'PNG': [0x89, 0x50, 0x4E, 0x47],
            'JPEG': [0xFF, 0xD8, 0xFF],
            'ZIP': [0x50, 0x4B, 0x03, 0x04],
            'GIF': [0x47, 0x49, 0x46, 0x38]
        };
        
        const matches = [];
        for (let [type, prefix] of Object.entries(prefixes)) {
            if (bytes.length >= prefix.length) {
                let match = true;
                for (let i = 0; i < prefix.length; i++) {
                    if (bytes[i] !== prefix[i]) {
                        match = false;
                        break;
                    }
                }
                if (match) {
                    matches.push(type);
                }
            }
        }
        
        return matches;
    }
    
    // 字节统计
    function getByteStatistics(bytes) {
        const stats = {
            min: Math.min(...bytes),
            max: Math.max(...bytes),
            average: bytes.reduce((a, b) => a + b, 0) / bytes.length,
            nullBytes: bytes.filter(b => b === 0).length,
            printableChars: bytes.filter(b => b >= 32 && b <= 126).length
        };
        
        stats.printableRatio = stats.printableChars / bytes.length;
        
        return stats;
    }
    
    // 分析加密特征
    function analyzeEncryption(dataString) {
        const analysis = {
            possibleAlgorithms: [],
            keyLengthGuess: null,
            blockSizeGuess: null
        };
        
        const length = dataString.length / 2; // 字节长度
        
        // 根据长度猜测可能的算法
        if (length % 16 === 0) {
            analysis.possibleAlgorithms.push('AES');
            analysis.blockSizeGuess = 16;
        }
        if (length % 8 === 0) {
            analysis.possibleAlgorithms.push('DES/3DES');
        }
        if (length === 32) {
            analysis.possibleAlgorithms.push('MD5 Hash');
        }
        if (length === 40) {
            analysis.possibleAlgorithms.push('SHA-1 Hash');
        }
        if (length === 64) {
            analysis.possibleAlgorithms.push('SHA-256 Hash');
        }
        
        return analysis;
    }
    
    // 保存解析结果
    function saveParseResult(result) {
        if (CONFIG.saveToStorage) {
            const key = `parsed_data_${Date.now()}`;
            GM_setValue(key, JSON.stringify(result));
            log('info', `解析结果已保存: ${key}`);
        }
    }
    
    // 显示解析结果
    function displayParseResult(originalResponse, parseResult) {
        // 创建详细的显示窗口
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 999999;
            display: flex;
            justify-content: center;
            align-items: center;
        `;
        
        const content = document.createElement('div');
        content.style.cssText = `
            background: white;
            border-radius: 10px;
            padding: 20px;
            max-width: 80%;
            max-height: 80%;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        `;
        
        content.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2 style="margin: 0; color: #333;">🔍 数据解析结果</h2>
                <button onclick="this.closest('[style*=\"position: fixed\"]').remove()" 
                        style="background: #ff4444; color: white; border: none; border-radius: 5px; padding: 5px 10px; cursor: pointer;">关闭</button>
            </div>
            
            <div style="margin-bottom: 15px;">
                <h3>基本信息</h3>
                <p><strong>时间:</strong> ${parseResult.timestamp}</p>
                <p><strong>数据长度:</strong> ${parseResult.length} 字符 (${parseResult.analysis.byteCount || 'N/A'} 字节)</p>
                <p><strong>数据类型:</strong> ${parseResult.analysis.isHex ? '十六进制' : '未知'}</p>
            </div>
            
            ${parseResult.analysis.encryption ? `
                <div style="margin-bottom: 15px;">
                    <h3>加密分析</h3>
                    <p><strong>可能的算法:</strong> ${parseResult.analysis.encryption.possibleAlgorithms.join(', ') || '未知'}</p>
                    <p><strong>块大小:</strong> ${parseResult.analysis.encryption.blockSizeGuess || '未知'}</p>
                </div>
            ` : ''}
            
            ${parseResult.analysis.patterns ? `
                <div style="margin-bottom: 15px;">
                    <h3>数据特征</h3>
                    <p><strong>熵值:</strong> ${parseResult.analysis.patterns.entropy.value.toFixed(2)} / 8 (${parseResult.analysis.patterns.entropy.interpretation})</p>
                    <p><strong>可打印字符比例:</strong> ${(parseResult.analysis.patterns.statisticalAnalysis.printableRatio * 100).toFixed(1)}%</p>
                </div>
            ` : ''}
            
            <div style="margin-bottom: 15px;">
                <h3>原始数据 (前200字符)</h3>
                <textarea readonly style="width: 100%; height: 100px; font-family: monospace; font-size: 10px;">${parseResult.original.substring(0, 200)}${parseResult.original.length > 200 ? '...' : ''}</textarea>
            </div>
            
            ${parseResult.decodingAttempts ? `
                <div style="margin-bottom: 15px;">
                    <h3>解码尝试</h3>
                    ${Object.entries(parseResult.decodingAttempts).map(([method, result]) => `
                        <div style="margin-bottom: 10px;">
                            <strong>${method}:</strong><br>
                            <textarea readonly style="width: 100%; height: 60px; font-family: monospace; font-size: 10px;">${typeof result === 'string' ? result.substring(0, 200) : JSON.stringify(result).substring(0, 200)}${(typeof result === 'string' ? result : JSON.stringify(result)).length > 200 ? '...' : ''}</textarea>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
            
            <div style="text-align: center; margin-top: 20px;">
                <button onclick="navigator.clipboard.writeText('${parseResult.original}')" 
                        style="background: #4CAF50; color: white; border: none; border-radius: 5px; padding: 8px 16px; margin: 0 5px; cursor: pointer;">复制原始数据</button>
                <button onclick="console.log('完整解析结果:', ${JSON.stringify(parseResult).replace(/'/g, "\\'")})" 
                        style="background: #2196F3; color: white; border: none; border-radius: 5px; padding: 8px 16px; margin: 0 5px; cursor: pointer;">输出到控制台</button>
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }
    
    // 主处理函数
    function handleTargetApiResponse(response, url) {
        if (response && response.code === 200 && response.data && typeof response.data === 'string') {
            log('info', '检测到目标API响应', { url, dataLength: response.data.length });
            
            // 解析数据
            const parseResult = advancedDataParser(response.data);
            
            // 保存结果
            saveParseResult(parseResult);
            
            // 显示通知
            if (CONFIG.showNotifications && typeof GM_notification !== 'undefined') {
                GM_notification({
                    title: '数据拦截成功',
                    text: `解析了 ${parseResult.length} 字符的数据`,
                    timeout: 3000
                });
            }
            
            // 显示详细结果
            setTimeout(() => {
                displayParseResult(response, parseResult);
            }, 500);
            
            return parseResult;
        }
    }
    
    // 拦截网络请求 - 与之前相同的拦截逻辑
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .then(response => {
                const clonedResponse = response.clone();
                clonedResponse.text().then(text => {
                    try {
                        const jsonData = JSON.parse(text);
                        handleTargetApiResponse(jsonData, args[0]);
                    } catch (e) {
                        // 忽略非JSON响应
                    }
                }).catch(error => {
                    log('error', '读取响应数据时出错', error);
                });
                return response;
            });
    };
    
    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
        this.addEventListener('load', function() {
            try {
                if (this.responseText) {
                    const jsonData = JSON.parse(this.responseText);
                    handleTargetApiResponse(jsonData, this._url);
                }
            } catch (e) {
                // 忽略非JSON响应
            }
        });
        return originalXHRSend.apply(this, args);
    };
    
    log('info', '专用数据解析器已启动');
    
})();
