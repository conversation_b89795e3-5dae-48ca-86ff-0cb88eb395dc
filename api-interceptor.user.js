// ==UserScript==
// @name         API数据拦截器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  拦截API返回数据并解析data字段
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('API拦截器已启动');
    
    // 创建一个解析data字段的函数
    function parseDataField(data) {
        try {
            // 如果data是十六进制字符串，尝试转换
            if (typeof data === 'string' && /^[0-9a-fA-F]+$/.test(data)) {
                console.log('检测到十六进制数据，长度:', data.length);
                
                // 尝试将十六进制转换为字符串
                let decoded = '';
                for (let i = 0; i < data.length; i += 2) {
                    const hex = data.substr(i, 2);
                    const charCode = parseInt(hex, 16);
                    if (charCode >= 32 && charCode <= 126) { // 可打印ASCII字符
                        decoded += String.fromCharCode(charCode);
                    } else {
                        decoded += '.'; // 不可打印字符用点表示
                    }
                }
                
                console.log('十六进制解码结果:', decoded);
                
                // 尝试Base64解码
                try {
                    const base64Decoded = atob(data);
                    console.log('Base64解码结果:', base64Decoded);
                } catch (e) {
                    console.log('Base64解码失败');
                }
                
                // 尝试作为加密数据处理
                console.log('可能是加密数据，原始长度:', data.length);
                console.log('前100个字符:', data.substring(0, 100));
                
                return {
                    type: 'hex_string',
                    original: data,
                    decoded: decoded,
                    length: data.length
                };
            }
            
            // 如果是JSON字符串，尝试解析
            if (typeof data === 'string') {
                try {
                    const parsed = JSON.parse(data);
                    console.log('JSON解析成功:', parsed);
                    return {
                        type: 'json',
                        parsed: parsed
                    };
                } catch (e) {
                    console.log('不是有效的JSON字符串');
                }
            }
            
            // 直接返回数据
            return {
                type: 'unknown',
                data: data
            };
            
        } catch (error) {
            console.error('解析data字段时出错:', error);
            return {
                type: 'error',
                error: error.message,
                original: data
            };
        }
    }
    
    // 处理API响应的函数
    function handleApiResponse(response, url) {
        try {
            if (response && response.code === 200 && response.data) {
                console.log('=== 检测到目标API响应 ===');
                console.log('URL:', url);
                console.log('完整响应:', response);
                console.log('Data字段:', response.data);
                
                // 解析data字段
                const parsedData = parseDataField(response.data);
                console.log('解析结果:', parsedData);
                
                // 在页面上显示解析结果（可选）
                showParseResult(response, parsedData, url);
                
                // 这里可以添加您的自定义处理逻辑
                // 例如：发送到其他服务器、保存到本地存储等
                
                return parsedData;
            }
        } catch (error) {
            console.error('处理API响应时出错:', error);
        }
    }
    
    // 在页面上显示解析结果
    function showParseResult(response, parsedData, url) {
        // 创建一个浮动窗口显示结果
        const resultDiv = document.createElement('div');
        resultDiv.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 300px;
            background: #f0f0f0;
            border: 2px solid #333;
            border-radius: 5px;
            padding: 10px;
            z-index: 10000;
            font-family: monospace;
            font-size: 12px;
            overflow-y: auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        `;
        
        resultDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #333;">
                🔍 API数据拦截结果
                <button onclick="this.parentElement.parentElement.remove()" style="float: right; background: #ff4444; color: white; border: none; border-radius: 3px; cursor: pointer;">×</button>
            </div>
            <div><strong>URL:</strong> ${url}</div>
            <div><strong>响应码:</strong> ${response.code}</div>
            <div><strong>消息:</strong> ${response.msg}</div>
            <div><strong>Data类型:</strong> ${parsedData.type}</div>
            <div><strong>Data长度:</strong> ${response.data.length}</div>
            <div style="margin-top: 10px;">
                <strong>原始Data:</strong><br>
                <textarea readonly style="width: 100%; height: 60px; font-size: 10px;">${response.data.substring(0, 200)}${response.data.length > 200 ? '...' : ''}</textarea>
            </div>
            ${parsedData.decoded ? `
                <div style="margin-top: 10px;">
                    <strong>解码结果:</strong><br>
                    <textarea readonly style="width: 100%; height: 60px; font-size: 10px;">${parsedData.decoded.substring(0, 200)}${parsedData.decoded.length > 200 ? '...' : ''}</textarea>
                </div>
            ` : ''}
        `;
        
        document.body.appendChild(resultDiv);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (resultDiv.parentNode) {
                resultDiv.remove();
            }
        }, 10000);
    }
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        return originalFetch.apply(this, args)
            .then(response => {
                // 克隆响应以便读取
                const clonedResponse = response.clone();
                
                // 异步处理响应数据
                clonedResponse.text().then(text => {
                    try {
                        const jsonData = JSON.parse(text);
                        handleApiResponse(jsonData, args[0]);
                    } catch (e) {
                        // 不是JSON响应，忽略
                    }
                }).catch(error => {
                    console.error('读取响应数据时出错:', error);
                });
                
                return response;
            })
            .catch(error => {
                console.error('Fetch请求出错:', error);
                throw error;
            });
    };
    
    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
        this.addEventListener('load', function() {
            try {
                if (this.responseText) {
                    const jsonData = JSON.parse(this.responseText);
                    handleApiResponse(jsonData, this._url);
                }
            } catch (e) {
                // 不是JSON响应，忽略
            }
        });
        
        return originalXHRSend.apply(this, args);
    };
    
    console.log('API拦截器设置完成');
    
})();
