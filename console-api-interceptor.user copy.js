// ==UserScript==
// @name         Console API拦截器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  拦截API返回数据，仅输出到console，每个接口单独显示
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('%c🚀 Console API拦截器已启动', 'background: #4CAF50; color: white; padding: 2px 8px; border-radius: 3px; font-weight: bold;');
    
    let requestCounter = 0;

    // PKCS#7填充移除函数
    function removePkcs7Padding(data) {
        if (data.length === 0) return data;

        const paddingLength = data[data.length - 1];

        // 验证填充是否有效
        if (paddingLength > 16 || paddingLength > data.length) {
            return data; // 无效填充，返回原数据
        }

        // 检查填充字节是否都相同
        for (let i = data.length - paddingLength; i < data.length; i++) {
            if (data[i] !== paddingLength) {
                return data; // 无效填充，返回原数据
            }
        }

        // 移除填充
        return data.slice(0, data.length - paddingLength);
    }

    // AES解密功能 - 使用Web Crypto API
    async function aesDecrypt(encryptedHex, key, mode = 'ECB', padding = 'pkcs7') {
        try {
            // 将十六进制字符串转换为Uint8Array
            const encryptedBytes = new Uint8Array(encryptedHex.match(/.{2}/g).map(byte => parseInt(byte, 16)));

            // 处理密钥
            let keyBytes;
            if (typeof key === 'string') {
                // 如果密钥是十六进制字符串
                if (/^[0-9a-fA-F]+$/.test(key)) {
                    keyBytes = new Uint8Array(key.match(/.{2}/g).map(byte => parseInt(byte, 16)));
                } else {
                    // UTF-8字符串转字节
                    keyBytes = new TextEncoder().encode(key);
                }
            }

            // 确保密钥长度正确 (16, 24, 或 32 字节)
            if (keyBytes.length < 16) {
                // 填充到16字节
                const paddedKey = new Uint8Array(16);
                paddedKey.set(keyBytes);
                keyBytes = paddedKey;
            } else if (keyBytes.length > 32) {
                // 截断到32字节
                keyBytes = keyBytes.slice(0, 32);
            } else if (keyBytes.length > 16 && keyBytes.length < 24) {
                // 填充到24字节
                const paddedKey = new Uint8Array(24);
                paddedKey.set(keyBytes);
                keyBytes = paddedKey;
            } else if (keyBytes.length > 24 && keyBytes.length < 32) {
                // 填充到32字节
                const paddedKey = new Uint8Array(32);
                paddedKey.set(keyBytes);
                keyBytes = paddedKey;
            }

            // 导入密钥
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                keyBytes,
                { name: 'AES-ECB' },
                false,
                ['decrypt']
            );

            // 解密
            const decryptedBuffer = await crypto.subtle.decrypt(
                { name: 'AES-ECB' },
                cryptoKey,
                encryptedBytes
            );

            // 转换为字节数组
            let decryptedBytes = new Uint8Array(decryptedBuffer);

            // 处理填充
            if (padding.toLowerCase() === 'pkcs7') {
                decryptedBytes = removePkcs7Padding(decryptedBytes);
            }

            // 转换为字符串
            const decryptedText = new TextDecoder('utf-8').decode(decryptedBytes);

            return {
                success: true,
                decrypted: decryptedText,
                keyUsed: key,
                keyLength: keyBytes.length * 8,
                mode: mode,
                padding: padding,
                originalLength: new Uint8Array(decryptedBuffer).length,
                unpaddedLength: decryptedBytes.length
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                keyUsed: key,
                mode: mode,
                padding: padding
            };
        }
    }

    // 尝试多种常见密钥和填充模式进行解密
    async function tryDecryptWithCommonKeys(encryptedHex) {
        const commonKeys = [
            // 常见的测试密钥
            'jxxx2023xvpj2022',
            '1234567890123456',
            'abcdefghijklmnop',
            '0123456789abcdef'
        ];

        const paddingModes = ['pkcs7', 'none'];
        const results = [];
        let foundValidResult = false;

        for (const key of commonKeys) {
            for (const padding of paddingModes) {
                const result = await aesDecrypt(encryptedHex, key, 'ECB', padding);

                // 添加填充模式信息到结果中
                result.paddingMode = padding;
                results.push(result);

                // 如果解密成功且结果看起来像有效文本，优先显示
                if (result.success && result.decrypted && isPrintableText(result.decrypted) && !foundValidResult) {
                    foundValidResult = true;
                    // 将有效结果移到数组开头
                    results.unshift(results.pop());
                }
            }
        }

        return results;
    }

    // 检查文本是否为可打印文本
    function isPrintableText(text) {
        if (!text || text.length === 0) return false;

        // 检查是否包含大量可打印字符
        const printableCount = text.split('').filter(char => {
            const code = char.charCodeAt(0);
            return (code >= 32 && code <= 126) || code === 10 || code === 13 || code === 9;
        }).length;

        const printableRatio = printableCount / text.length;
        return printableRatio > 0.7; // 70%以上为可打印字符
    }

    // 简化的数据解析函数
    function parseData(data) {
        if (!data) return { type: 'empty', data: null };
        
        const result = {
            type: 'unknown',
            length: typeof data === 'string' ? data.length : JSON.stringify(data).length,
            preview: null,
            decoded: null
        };
        
        // 检查是否为十六进制字符串
        if (typeof data === 'string' && /^[0-9a-fA-F]+$/.test(data) && data.length > 10) {
            result.type = 'hex';
            result.preview = data.substring(0, 100) + (data.length > 100 ? '...' : '');
            
            // 尝试ASCII解码
            let decoded = '';
            for (let i = 0; i < Math.min(data.length, 200); i += 2) {
                const hex = data.substring(i, i + 2);
                const charCode = parseInt(hex, 16);
                if (charCode >= 32 && charCode <= 126) {
                    decoded += String.fromCharCode(charCode);
                } else {
                    decoded += '.';
                }
            }
            result.decoded = decoded;
        } else if (typeof data === 'string') {
            result.type = 'string';
            result.preview = data.substring(0, 200) + (data.length > 200 ? '...' : '');
        } else if (typeof data === 'object') {
            result.type = 'object';
            result.preview = JSON.stringify(data).substring(0, 200) + '...';
        } else {
            result.type = typeof data;
            result.preview = String(data);
        }
        
        return result;
    }
    
    // 格式化URL显示
    function formatUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.pathname}${urlObj.search}`;
        } catch {
            return url;
        }
    }
    
    // 主处理函数
    function processApiResponse(response, url, method = 'unknown') {
        requestCounter++;
        
        const timestamp = new Date().toLocaleTimeString();
        const shortUrl = formatUrl(url);
        
        console.group(`%c📡 [${requestCounter}] ${method} ${timestamp}`, 'color: #2196F3; font-weight: bold;');
        console.log(`🌐 ${shortUrl}`);
        
        // 检查响应格式
        if (response && typeof response === 'object') {
            // 显示基本信息
            const code = response.code || response.status || response.statusCode;
            const message = response.msg || response.message || response.statusText;
            
            if (code) console.log(`📊 状态: ${code}`);
            if (message) console.log(`💬 消息: ${message}`);
            
            // 处理data字段
            if (response.data !== undefined) {
                const parsedData = parseData(response.data);
                
                console.log(`%c📦 Data字段分析:`, 'color: #FF9800; font-weight: bold;');
                console.log(`   类型: ${parsedData.type}`);
                console.log(`   长度: ${parsedData.length}`);
                
                if (parsedData.type === 'hex') {
                    console.log(`   预览: ${parsedData.preview}`);
                    if (parsedData.decoded) {
                        console.log(`   ASCII解码: ${parsedData.decoded.substring(0, 100)}${parsedData.decoded.length > 100 ? '...' : ''}`);
                    }
                    console.log(`   完整数据:`, response.data);

                    // 添加AES解密尝试
                    console.log(`%c🔓 AES解密尝试:`, 'color: #E91E63; font-weight: bold;');
                    tryDecryptWithCommonKeys(response.data).then(decryptResults => {
                        let foundValidDecryption = false;

                        decryptResults.forEach((result) => {
                            if (result.success) {
                                const isValid = isPrintableText(result.decrypted);
                                const style = isValid ? 'color: #4CAF50; font-weight: bold;' : 'color: #FF9800;';
                                const status = isValid ? '✅ 成功' : '⚠️ 可能';
                                const paddingInfo = result.paddingMode || result.padding || 'unknown';

                                console.log(`%c   ${status} - 密钥: ${result.keyUsed} (${result.keyLength}位) - 填充: ${paddingInfo}`, style);

                                if (result.originalLength && result.unpaddedLength && result.originalLength !== result.unpaddedLength) {
                                    console.log(`      填充处理: ${result.originalLength} → ${result.unpaddedLength} 字节`);
                                }

                                console.log(`      解密结果: ${result.decrypted.substring(0, 200)}${result.decrypted.length > 200 ? '...' : ''}`);

                                if (isValid && !foundValidDecryption) {
                                    foundValidDecryption = true;
                                    console.log(`%c   📋 完整解密内容:`, 'color: #4CAF50;', result.decrypted);
                                }
                            } else {
                                const paddingInfo = result.paddingMode || result.padding || 'unknown';
                                console.log(`   ❌ 失败 - 密钥: ${result.keyUsed} - 填充: ${paddingInfo} - 错误: ${result.error}`);
                            }
                        });

                        if (!foundValidDecryption) {
                            console.log(`   💡 提示: 可能需要其他密钥或加密模式`);
                        }
                    }).catch(error => {
                        console.error(`   ❌ 解密过程出错:`, error);
                    });
                } else {
                    console.log(`   内容: ${parsedData.preview}`);
                    if (parsedData.type === 'object') {
                        console.log(`   完整对象:`, response.data);
                    }
                }
            }
            
            // 显示完整响应（折叠状态）
            console.log(`%c📋 完整响应:`, 'color: #9C27B0;', response);
            
        } else {
            console.log(`📄 响应内容:`, response);
        }
        
        console.groupEnd();
    }
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        const options = args[1] || {};
        const method = options.method || 'GET';
        
        return originalFetch.apply(this, args)
            .then(response => {
                // 克隆响应以便读取
                const clonedResponse = response.clone();
                
                // 异步处理响应数据
                clonedResponse.text().then(text => {
                    try {
                        const jsonData = JSON.parse(text);
                        processApiResponse(jsonData, url, method);
                    } catch (e) {
                        // 非JSON响应，简单记录
                        if (text.length > 0) {
                            console.log(`%c📡 [${++requestCounter}] ${method} ${new Date().toLocaleTimeString()}`, 'color: #9E9E9E;');
                            console.log(`🌐 ${formatUrl(url)}`);
                            console.log(`📄 非JSON响应 (${text.length}字符):`, text.substring(0, 200) + (text.length > 200 ? '...' : ''));
                        }
                    }
                }).catch(error => {
                    console.error('读取响应数据时出错:', error);
                });
                
                return response;
            })
            .catch(error => {
                console.error(`❌ Fetch请求失败 [${++requestCounter}]:`, url, error);
                throw error;
            });
    };
    
    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
        this.addEventListener('load', function() {
            try {
                if (this.responseText) {
                    try {
                        const jsonData = JSON.parse(this.responseText);
                        processApiResponse(jsonData, this._url, this._method);
                    } catch (e) {
                        // 非JSON响应，简单记录
                        if (this.responseText.length > 0) {
                            console.log(`%c📡 [${++requestCounter}] ${this._method} ${new Date().toLocaleTimeString()}`, 'color: #9E9E9E;');
                            console.log(`🌐 ${formatUrl(this._url)}`);
                            console.log(`📄 非JSON响应 (${this.responseText.length}字符):`, this.responseText.substring(0, 200) + (this.responseText.length > 200 ? '...' : ''));
                        }
                    }
                }
            } catch (error) {
                console.error('处理XHR响应时出错:', error);
            }
        });
        
        return originalXHRSend.apply(this, args);
    };
    
    // 添加一些有用的console命令
    window.apiInterceptor = {
        getStats: () => {
            console.log(`📊 已拦截 ${requestCounter} 个请求`);
            return requestCounter;
        },
        clear: () => {
            console.clear();
            requestCounter = 0;
            console.log('%c🧹 Console已清空，计数器已重置', 'color: #4CAF50;');
        },
        // 手动解密功能
        decrypt: async (encryptedHex, key = null, padding = 'pkcs7') => {
            console.log(`%c🔓 手动解密`, 'color: #E91E63; font-weight: bold;');
            console.log(`📦 加密数据: ${encryptedHex.substring(0, 100)}${encryptedHex.length > 100 ? '...' : ''}`);
            console.log(`🔧 填充模式: ${padding}`);

            if (key) {
                // 使用指定密钥解密
                const result = await aesDecrypt(encryptedHex, key, 'ECB', padding);
                if (result.success) {
                    console.log(`%c✅ 解密成功`, 'color: #4CAF50; font-weight: bold;');
                    console.log(`🔑 密钥: ${result.keyUsed} (${result.keyLength}位)`);
                    console.log(`� 填充: ${result.padding}`);
                    if (result.originalLength && result.unpaddedLength && result.originalLength !== result.unpaddedLength) {
                        console.log(`📏 填充处理: ${result.originalLength} → ${result.unpaddedLength} 字节`);
                    }
                    console.log(`�📄 解密结果: ${result.decrypted}`);
                    return result.decrypted;
                } else {
                    console.log(`%c❌ 解密失败`, 'color: #F44336; font-weight: bold;');
                    console.log(`🔑 密钥: ${result.keyUsed}`);
                    console.log(`🔧 填充: ${result.padding}`);
                    console.log(`❌ 错误: ${result.error}`);
                    return null;
                }
            } else {
                // 尝试常见密钥
                const results = await tryDecryptWithCommonKeys(encryptedHex);
                let foundValid = false;

                results.forEach((result) => {
                    if (result.success) {
                        const isValid = isPrintableText(result.decrypted);
                        if (isValid && !foundValid) {
                            foundValid = true;
                            console.log(`%c✅ 找到有效解密`, 'color: #4CAF50; font-weight: bold;');
                            console.log(`🔑 密钥: ${result.keyUsed} (${result.keyLength}位)`);
                            console.log(`� 填充: ${result.paddingMode || result.padding}`);
                            if (result.originalLength && result.unpaddedLength && result.originalLength !== result.unpaddedLength) {
                                console.log(`📏 填充处理: ${result.originalLength} → ${result.unpaddedLength} 字节`);
                            }
                            console.log(`�📄 解密结果: ${result.decrypted}`);
                            return result.decrypted;
                        }
                    }
                });

                if (!foundValid) {
                    console.log(`%c⚠️ 未找到有效解密`, 'color: #FF9800; font-weight: bold;');
                    console.log(`💡 请尝试: apiInterceptor.decrypt('数据', '密钥', 'pkcs7') 或 apiInterceptor.decrypt('数据', '密钥', 'none')`);
                }

                return results;
            }
        },
        // 设置自定义密钥列表
        setKeys: (keyArray) => {
            if (Array.isArray(keyArray)) {
                // 这里可以扩展为动态密钥列表
                console.log(`%c🔑 已设置 ${keyArray.length} 个自定义密钥`, 'color: #2196F3;');
                console.log(keyArray);
            } else {
                console.log(`%c❌ 请提供密钥数组`, 'color: #F44336;');
            }
        }
    };
    
    console.log('%c✅ 拦截器设置完成 - 已集成AES解密功能 (支持PKCS#7填充)', 'color: #4CAF50; font-weight: bold;');
    console.log('%c💡 可用命令:', 'color: #607D8B; font-weight: bold;');
    console.log('%c   apiInterceptor.getStats() - 查看拦截统计', 'color: #607D8B;');
    console.log('%c   apiInterceptor.clear() - 清空console', 'color: #607D8B;');
    console.log('%c   apiInterceptor.decrypt(data, key, padding) - 手动解密数据', 'color: #607D8B;');
    console.log('%c     示例: apiInterceptor.decrypt("3277ef...", "jxxx2023xvpj2022", "pkcs7")', 'color: #9E9E9E;');
    console.log('%c   apiInterceptor.setKeys([key1, key2]) - 设置自定义密钥', 'color: #607D8B;');
    console.log('%c🔧 支持的填充模式: pkcs7 (默认), none', 'color: #607D8B;');
    
})();
