// ==UserScript==
// @name         Console API拦截器
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  拦截API返回数据，仅输出到console，每个接口单独显示
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('%c🚀 Console API拦截器已启动', 'background: #4CAF50; color: white; padding: 2px 8px; border-radius: 3px; font-weight: bold;');
    
    let requestCounter = 0;
    
    // 简化的数据解析函数
    function parseData(data) {
        if (!data) return { type: 'empty', data: null };
        
        const result = {
            type: 'unknown',
            length: typeof data === 'string' ? data.length : JSON.stringify(data).length,
            preview: null,
            decoded: null
        };
        
        // 检查是否为十六进制字符串
        if (typeof data === 'string' && /^[0-9a-fA-F]+$/.test(data) && data.length > 10) {
            result.type = 'hex';
            result.preview = data.substring(0, 100) + (data.length > 100 ? '...' : '');
            
            // 尝试ASCII解码
            let decoded = '';
            for (let i = 0; i < Math.min(data.length, 200); i += 2) {
                const hex = data.substring(i, i + 2);
                const charCode = parseInt(hex, 16);
                if (charCode >= 32 && charCode <= 126) {
                    decoded += String.fromCharCode(charCode);
                } else {
                    decoded += '.';
                }
            }
            result.decoded = decoded;
        } else if (typeof data === 'string') {
            result.type = 'string';
            result.preview = data.substring(0, 200) + (data.length > 200 ? '...' : '');
        } else if (typeof data === 'object') {
            result.type = 'object';
            result.preview = JSON.stringify(data).substring(0, 200) + '...';
        } else {
            result.type = typeof data;
            result.preview = String(data);
        }
        
        return result;
    }
    
    // 格式化URL显示
    function formatUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.pathname}${urlObj.search}`;
        } catch {
            return url;
        }
    }
    
    // 主处理函数
    function processApiResponse(response, url, method = 'unknown') {
        requestCounter++;
        
        const timestamp = new Date().toLocaleTimeString();
        const shortUrl = formatUrl(url);
        
        console.group(`%c📡 [${requestCounter}] ${method} ${timestamp}`, 'color: #2196F3; font-weight: bold;');
        console.log(`🌐 ${shortUrl}`);
        
        // 检查响应格式
        if (response && typeof response === 'object') {
            // 显示基本信息
            const code = response.code || response.status || response.statusCode;
            const message = response.msg || response.message || response.statusText;
            
            if (code) console.log(`📊 状态: ${code}`);
            if (message) console.log(`💬 消息: ${message}`);
            
            // 处理data字段
            if (response.data !== undefined) {
                const parsedData = parseData(response.data);
                
                console.log(`%c📦 Data字段分析:`, 'color: #FF9800; font-weight: bold;');
                console.log(`   类型: ${parsedData.type}`);
                console.log(`   长度: ${parsedData.length}`);
                
                if (parsedData.type === 'hex') {
                    console.log(`   预览: ${parsedData.preview}`);
                    if (parsedData.decoded) {
                        console.log(`   ASCII解码: ${parsedData.decoded.substring(0, 100)}${parsedData.decoded.length > 100 ? '...' : ''}`);
                    }
                    console.log(`   完整数据:`, response.data);
                } else {
                    console.log(`   内容: ${parsedData.preview}`);
                    if (parsedData.type === 'object') {
                        console.log(`   完整对象:`, response.data);
                    }
                }
            }
            
            // 显示完整响应（折叠状态）
            console.log(`%c📋 完整响应:`, 'color: #9C27B0;', response);
            
        } else {
            console.log(`📄 响应内容:`, response);
        }
        
        console.groupEnd();
    }
    
    // 拦截fetch请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const url = args[0];
        const options = args[1] || {};
        const method = options.method || 'GET';
        
        return originalFetch.apply(this, args)
            .then(response => {
                // 克隆响应以便读取
                const clonedResponse = response.clone();
                
                // 异步处理响应数据
                clonedResponse.text().then(text => {
                    try {
                        const jsonData = JSON.parse(text);
                        processApiResponse(jsonData, url, method);
                    } catch (e) {
                        // 非JSON响应，简单记录
                        if (text.length > 0) {
                            console.log(`%c📡 [${++requestCounter}] ${method} ${new Date().toLocaleTimeString()}`, 'color: #9E9E9E;');
                            console.log(`🌐 ${formatUrl(url)}`);
                            console.log(`📄 非JSON响应 (${text.length}字符):`, text.substring(0, 200) + (text.length > 200 ? '...' : ''));
                        }
                    }
                }).catch(error => {
                    console.error('读取响应数据时出错:', error);
                });
                
                return response;
            })
            .catch(error => {
                console.error(`❌ Fetch请求失败 [${++requestCounter}]:`, url, error);
                throw error;
            });
    };
    
    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
        this.addEventListener('load', function() {
            try {
                if (this.responseText) {
                    try {
                        const jsonData = JSON.parse(this.responseText);
                        processApiResponse(jsonData, this._url, this._method);
                    } catch (e) {
                        // 非JSON响应，简单记录
                        if (this.responseText.length > 0) {
                            console.log(`%c📡 [${++requestCounter}] ${this._method} ${new Date().toLocaleTimeString()}`, 'color: #9E9E9E;');
                            console.log(`🌐 ${formatUrl(this._url)}`);
                            console.log(`📄 非JSON响应 (${this.responseText.length}字符):`, this.responseText.substring(0, 200) + (this.responseText.length > 200 ? '...' : ''));
                        }
                    }
                }
            } catch (error) {
                console.error('处理XHR响应时出错:', error);
            }
        });
        
        return originalXHRSend.apply(this, args);
    };
    
    // 添加一些有用的console命令
    window.apiInterceptor = {
        getStats: () => {
            console.log(`📊 已拦截 ${requestCounter} 个请求`);
            return requestCounter;
        },
        clear: () => {
            console.clear();
            requestCounter = 0;
            console.log('%c🧹 Console已清空，计数器已重置', 'color: #4CAF50;');
        }
    };
    
    console.log('%c✅ 拦截器设置完成', 'color: #4CAF50; font-weight: bold;');
    console.log('%c💡 提示: 使用 apiInterceptor.getStats() 查看统计，apiInterceptor.clear() 清空console', 'color: #607D8B;');
    
})();
